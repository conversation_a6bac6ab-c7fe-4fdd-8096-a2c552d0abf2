<?php

namespace App\Http\Controllers\Client;

use App\Models\Post;
use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class ReportController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/common.min.js','assets/js/clients/datatable-report.min.js']);
        return view('clients.reports.index');
    }

    public function report(Request $request)
    {
        $data = Transaction::query()->select('id', 'code','balance_before', 'balance_after', 'amount', 'created_at', 'status', 'description', 'type','math');
        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('code', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }
}
