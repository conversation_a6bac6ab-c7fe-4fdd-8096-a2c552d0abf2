<?php

namespace App\Http\Controllers\Webhook;

use App\Datas\BankWebhookData;
use App\Events\BankWebhookEvent;
use App\Http\Controllers\Controller;
use App\Models\TransactionBank;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
class BankWebhookController extends Controller
{
    /**
     * @return Response|ResponseFactory
     *
     * @throws BindingResolutionException
     */
    public function webhook(Request $request)
    {
        $token = $this->bearerToken($request);

        if (tenant_setting('acb_public')){
            throw_if(
                tenant_setting('acb_token') && $token !== tenant_setting('acb_token'),
                ValidationException::withMessages(['message' => ['Invalid Token']])
            );
        }

        $sePayWebhookData = new BankWebhookData(
            $request->integer('id'),
            $request->string('gateway')->value(),
            $request->string('transactionDate')->value(),
            $request->string('accountNumber')->value(),
            $request->string('subAccount')->value(),
            $request->string('code')->value(),
            $request->string('content')->value(),
            $request->string('transferType')->value(),
            $request->string('description')->value(),
            $request->integer('transferAmount'),
            $request->string('referenceCode')->value(),
            $request->integer('accumulated')
        );

        throw_if(
            TransactionBank::query()->whereId($sePayWebhookData->id)->exists(),
            ValidationException::withMessages(['message' => ['transaction này đã thực hiện']])
        );

        $model = new TransactionBank();
        $model->id = $sePayWebhookData->id;
        $model->gateway = $sePayWebhookData->gateway;
        $model->transactionDate = $sePayWebhookData->transactionDate;
        $model->accountNumber = $sePayWebhookData->accountNumber;
        $model->subAccount = $sePayWebhookData->subAccount;
        $model->code = $sePayWebhookData->code;
        $model->content = $sePayWebhookData->content;
        $model->transferType = $sePayWebhookData->transferType;
        $model->description = $sePayWebhookData->description;
        $model->transferAmount = $sePayWebhookData->transferAmount;
        $model->referenceCode = $sePayWebhookData->referenceCode;
        $model->save();

        $pattern = '/\b'.tenant_setting('transfer_content').'([a-zA-Z0-9-_])+/';
        preg_match($pattern, $sePayWebhookData->content, $matches);

        if (isset($matches[0])) {
            $info = Str::of($matches[0])->replaceFirst(tenant_setting('transfer_content'), '')->value();
            event(new BankWebhookEvent($info, $sePayWebhookData));
        }

        return response()->noContent();
    }

    /**
     * Get the bearer token from the request headers.
     *
     * @return string|null
     */
    private function bearerToken(Request $request)
    {
        $header = $request->header('Authorization', '');

        $position = strrpos($header, 'Apikey ');

        if ($position !== false) {
            $header = substr($header, $position + 7);

            return str_contains($header, ',') ? (strstr($header, ',', true) ?: null) : $header;
        }

        return null;
    }
}
