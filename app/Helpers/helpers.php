<?php

/**
 * Get current tenant instance
 *
 * @return \App\Models\Tenant|null
 */
function current_tenant(): ?\App\Models\Tenant
{
    try {
        return app('currentTenant');
    } catch (\Illuminate\Contracts\Container\BindingResolutionException) {
        return null;
    }
}

/**
 * Get tenant setting value by key
 *
 * @param string $key The setting key
 * @param mixed $default Default value if setting not found
 * @param int|null $tenantId Specific tenant ID (uses current tenant if null)
 * @return mixed The setting value or default
 */
function tenant_setting(string $key, $default = null, ?int $tenantId = null)
{
    // Get tenant ID from parameter or current tenant
    if ($tenantId === null) {
        $tenant = current_tenant();
        $tenantId = $tenant?->id;
    }

    // If no tenant ID available, return default
    if (!$tenantId) {
        return $default;
    }

    // Try to get from cache first
    $cacheKey = "tenant_setting_{$tenantId}_{$key}";
    if (cache()->has($cacheKey)) {
        return cache()->get($cacheKey);
    }

    // Get from database
    $setting = \App\Models\TenantSetting::where('tenant_id', $tenantId)
        ->where('key', $key)
        ->first();

    $value = $setting ? $setting->value : $default;

    // Cache the result for 60 minutes
    cache()->put($cacheKey, $value, now()->addMinutes(60));

    return $value;
}

/**
 * Get all settings for a tenant
 *
 * @param int|null $tenantId Specific tenant ID (uses current tenant if null)
 * @return array Array of settings as key => value pairs
 */
function tenant_settings(?int $tenantId = null): array
{
    // Get tenant ID from parameter or current tenant
    if ($tenantId === null) {
        $tenant = current_tenant();
        $tenantId = $tenant?->id;
    }

    // If no tenant ID available, return empty array
    if (!$tenantId) {
        return [];
    }

    // Try to get from cache first
    $cacheKey = "tenant_settings_{$tenantId}";
    if (cache()->has($cacheKey)) {
        return cache()->get($cacheKey);
    }

    // Get from database
    $settings = \App\Models\TenantSetting::where('tenant_id', $tenantId)
        ->pluck('value', 'key')
        ->toArray();

    // Cache the result for 60 minutes
    cache()->put($cacheKey, $settings, now()->addMinutes(60));

    return $settings;
}

/**
 * Clear tenant settings cache
 *
 * @param int|null $tenantId Specific tenant ID (uses current tenant if null)
 * @param string|null $key Specific key to clear (clears all if null)
 * @return void
 */
function clear_tenant_settings_cache(?int $tenantId = null, ?string $key = null): void
{
    // Get tenant ID from parameter or current tenant
    if ($tenantId === null) {
        $tenant = current_tenant();
        $tenantId = $tenant?->id;
    }

    // If no tenant ID available, return
    if (!$tenantId) {
        return;
    }

    // Clear specific key or all settings
    if ($key) {
        cache()->forget("tenant_setting_{$tenantId}_{$key}");
    } else {
        cache()->forget("tenant_settings_{$tenantId}");

        // Also clear individual setting keys
        $settings = \App\Models\TenantSetting::query()->where('tenant_id', $tenantId)->get();
        foreach ($settings as $setting) {
            cache()->forget("tenant_setting_{$tenantId}_{$setting->key}");
        }
    }
}

if (!function_exists('isActiveMenu')) {
    /**
     * Check if current route matches the platform and category
     *
     * @param string $platform Platform key
     * @param string|null $categoryKey Category key (optional)
     * @return bool
     */
    function isActiveMenu(string $platform, ?string $categoryKey = null): bool
    {
        $routePlatform = request()->route('platform');
        $routeCategory = request()->route('category');

        // Check platform match
        if ($routePlatform !== $platform) {
            return false;
        }

        // If category is provided, check category match
        if ($categoryKey !== null) {
            return $routeCategory === $categoryKey;
        }

        // If only platform is provided, it's active if platform matches
        return true;
    }
}

if (!function_exists('isActiveGroup')) {
    /**
     * Check if current route matches the platform and group
     *
     * @param string $platform Platform key
     * @param string $groupKey Group key
     * @return bool
     */
    function isActiveGroup(string $platform, string $groupKey): bool
    {
        $routePlatform = request()->route('platform');
        $routeCategory = request()->route('category');

        // Check platform match
        if ($routePlatform !== $platform) {
            return false;
        }

        // Check if category starts with group key
        if ($routeCategory && str_starts_with($routeCategory, $groupKey)) {
            return true;
        }

        return false;
    }
}

if (!function_exists('menuCollapseClass')) {
    /**
     * Get collapse class based on active state
     *
     * @param bool $active Whether the menu is active
     * @param string $type Type of element ('div' or 'button')
     * @return string CSS class
     */
    function menuCollapseClass(bool $active, string $type = 'div'): string
    {
        return $type === 'div'
            ? ($active ? 'collapse show' : 'collapse')
            : ($active ? '' : 'collapsed');
    }
}

if (!function_exists('isGroupActive')) {
    /**
     * Check if a group is active based on current platform and category
     *
     * @param string $platform Platform key
     * @param string $groupKey Group key
     * @return bool
     */
    function isGroupActive(string $platform, string $groupKey): bool
    {
        $currentPlatform = request()->route('platform');
        $currentCategory = request()->route('category');

        if ($currentPlatform !== $platform) {
            return false;
        }

        if (!$currentCategory) {
            return false;
        }

        // Check if any service in this group matches the current category
        foreach (config('services_menu.platforms') as $p) {
            if ($p['key'] === $platform) {
                foreach ($p['groups'] ?? [] as $g) {
                    if ($g['key'] === $groupKey) {
                        foreach ($g['services'] as $service) {
                            if ($service['key'] === $currentCategory) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }
}
if (!function_exists('textToArray')) {
    function textToArray(string $string, bool $trim = true, bool $removeEmpty = true): array
    {
        $array = explode("\n", $string);

        if ($trim) {
            $array = array_map('trim', $array);
        }

        if ($removeEmpty) {
            $array = array_filter($array, function ($value) {
                return $value !== '';
            });
        }

        return array_values($array);
    }
}
