<?php

namespace App\Listeners;

use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Events\BankWebhookEvent;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class BankWebhookListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BankWebhookEvent $event): void
    {
        if(strtolower($event->bankWebhookData->transferType) === 'in'){
            $data = $event->bankWebhookData;
            $user = User::query()->where('code', $event->info)->first();
            if ($user) {
                $balanceBefore = $user->balance;
                $amount = $data->transferAmount;
                $balanceAfter = $balanceBefore + $amount;
                $user->balance = $balanceAfter;
                $user->total_deposit = $user->total_deposit + $amount;
                $user->save();

                $transaction = new Transaction();

                $transaction->user_id = $user->id;
                $transaction->tenant_id = $user->getTenantId() ?? 1; // Fallback to 1 if no tenant found
                $transaction->admin_id = $user->id;
                $transaction->type = TransactionTypeEnum::DEPOSIT->value;
                $transaction->math ='+';
                $transaction->amount = $amount;
                $transaction->balance_before = $balanceBefore;
                $transaction->balance_after = $balanceAfter;
                $transaction->status = TransactionStatusEnum::COMPLETED->value;
                $transaction->description = 'Nạp tiền qua ' . $data->gateway;
                $transaction->save();

            }
        }
    }
}
