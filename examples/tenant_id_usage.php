<?php

// <PERSON><PERSON> dụ sử dụng các cách lấy tenant_id

// 1. Trong Controller (có current tenant context)
class ExampleController extends Controller
{
    public function store(Request $request)
    {
        $user = auth()->user();
        
        // Cách 1: Sử dụng User model method (Khuyến nghị)
        $tenantId = $user->getTenantId() ?? 1;
        
        // Cách 2: Sử dụng current tenant
        $tenantId = current_tenant()?->id ?? 1;
        
        // Tạo record với tenant_id
        Model::create([
            'user_id' => $user->id,
            'tenant_id' => $tenantId,
            'data' => $request->data,
        ]);
    }
}

// 2. Trong Listener/Job (có thể không có current tenant context)
class ExampleListener
{
    public function handle($event)
    {
        $user = $event->user;
        
        // Cách 1: Sử dụng User model method (Khuyến nghị)
        $tenantId = $user->getTenantId() ?? 1;
        
        // Cách 2: Sử dụng helper function
        $tenantId = get_tenant_id($user) ?? 1;
        
        // Cách 3: <PERSON><PERSON><PERSON> có domain trong event
        if (isset($event->domain)) {
            $tenantId = get_tenant_id_by_domain($event->domain) ?? 1;
        }
        
        // Tạo record
        Transaction::create([
            'user_id' => $user->id,
            'tenant_id' => $tenantId,
            'amount' => $event->amount,
        ]);
    }
}

// 3. Trong Command/Artisan (không có web context)
class ExampleCommand extends Command
{
    public function handle()
    {
        $users = User::all();
        
        foreach ($users as $user) {
            // Sử dụng User model method
            $tenantId = $user->getTenantId();
            
            if ($tenantId) {
                // Xử lý với tenant_id
                $this->processUserForTenant($user, $tenantId);
            }
        }
    }
}

// 4. Trong API/Webhook (có domain)
class WebhookController extends Controller
{
    public function handle(Request $request)
    {
        // Lấy tenant từ domain của request
        $tenantId = get_tenant_id_by_domain() ?? 1;
        
        // Hoặc từ subdomain
        $domain = $request->getHost();
        $tenantId = get_tenant_id_by_domain($domain) ?? 1;
        
        // Xử lý webhook với tenant_id
        $this->processWebhook($request->all(), $tenantId);
    }
}

// 5. Trong Model Observer
class UserObserver
{
    public function created(User $user)
    {
        // Lấy tenant_id từ user hoặc current tenant
        $tenantId = $user->getTenantId() ?? current_tenant()?->id ?? 1;
        
        // Tạo log
        UserActivityLog::create([
            'user_id' => $user->id,
            'tenant_id' => $tenantId,
            'action' => 'created',
        ]);
    }
}

// 6. Trong Service Class
class TransactionService
{
    public function createTransaction(User $user, float $amount, string $type)
    {
        // Luôn ưu tiên lấy từ user trước
        $tenantId = $user->getTenantId();
        
        // Nếu không có, fallback to current tenant
        if (!$tenantId) {
            $tenantId = current_tenant()?->id;
        }
        
        // Nếu vẫn không có, throw exception thay vì dùng default
        if (!$tenantId) {
            throw new \Exception('Cannot determine tenant for transaction');
        }
        
        return Transaction::create([
            'user_id' => $user->id,
            'tenant_id' => $tenantId,
            'amount' => $amount,
            'type' => $type,
        ]);
    }
}
