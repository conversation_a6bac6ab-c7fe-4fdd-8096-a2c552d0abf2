"use strict";
import { definedColumns, xAjax,  reloadTable , components,ajaxUrl, show } from "../table";
var filter = {order:"order", trasaction:"transaction"}
$(document).ready(function() {
  reloadTable.datatableLog = $('#datatable-report').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/client/ajax/reports`),
    order: [[ 0, "desc" ]],
    columns: [
      definedColumns.stt,
      definedColumns.code,
      definedColumns.price,
      definedColumns.description,
      definedColumns.status,
      definedColumns.created_at,
    ]
  });

  
});
