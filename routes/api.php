<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::group([
    'prefix' => 'api/bank/webhook',
    'as' => 'bank-webhook.',
    'middleware' => ['api'],
], function () {
    Route::any('/webhook', [\App\Http\Controllers\Webhook\BankWebhookController::class, 'webhook'])->name('webhook');
});
