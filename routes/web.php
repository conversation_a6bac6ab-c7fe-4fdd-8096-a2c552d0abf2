<?php

use App\Http\Controllers\Client\DepositController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Client\ReportController;
use App\Http\Controllers\Client\ServiceController;
use App\Http\Controllers\Client\TransactionController;

require_once __DIR__. '/admin.php';
require_once __DIR__ . '/supper-admin.php';
Route::get('/', function () {
    return view('welcome');
});

Auth::routes();
Route::middleware('redirect.tenant')->as('client.')->group(function () {
    Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
    Route::get('/transactions', [TransactionController::class, 'index'])->name('transactions.index');
    Route::get('{platform}/{category}', [ServiceController::class, 'index'])->name('service.index');
    Route::get('/load/services/{platform}/{category}', [ServiceController::class, 'loadService'])->name('service.load');

    Route::post('/order/services',[\App\Http\Controllers\Client\OrderController::class, 'order'])->name('order.service');

    Route::get('/report',[ReportController::class, 'index'])->name('report.index');
    Route::get('/client/ajax/reports',[ReportController::class, 'report'])->name('report.ajax');

    Route::get('deposit',[DepositController::class, 'index'])->name('deposit.index');

});


