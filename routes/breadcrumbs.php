<?php

use App\Models\Category;
use App\Models\Platform;
use Diglactic\Breadcrumbs\Breadcrumbs;
use Diglactic\Breadcrumbs\Generator as BreadcrumbsTrait;
use Illuminate\Support\Facades\Route;

//Admin

Breadcrumbs::for('admin.dashboard', function (BreadcrumbsTrait $trail) {
    $trail->push('Trang quản trị');
});

Breadcrumbs::for('admin.members.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Thành viên');
});


Breadcrumbs::for('admin.members.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Chỉnh sửa thành viên');
});

Breadcrumbs::for('admin.members.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Thêm mới thành viên');
});

Breadcrumbs::for('admin.members.plus-money', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Cộng tiền thủ công');
});

Breadcrumbs::for('admin.members.minus-money', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Trừ tiền thủ công');
});

Breadcrumbs::for('admin.price-list.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Bảng giá dịch vụ');
});


Breadcrumbs::for('admin.transactions.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Giao dịch');
});

Breadcrumbs::for('supper-admin.platforms.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Nền tảng dịch vụ');
});

Breadcrumbs::for('supper-admin.platforms.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.platforms.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.platforms.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.platforms.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.categories.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Danh mục dịch vụ');
});

Breadcrumbs::for('supper-admin.categories.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.categories.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.categories.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.categories.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.providers.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Nhà cung cấp');
});

Breadcrumbs::for('supper-admin.providers.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.providers.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.providers.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.providers.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.package-lists.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Gói');
});

Breadcrumbs::for('supper-admin.package-lists.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.package-lists.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.package-lists.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.package-lists.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.packages.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Gói dich vụ');
});

Breadcrumbs::for('supper-admin.packages.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.packages.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.packages.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.packages.index');
    $trail->push('Chỉnh sửa');
});
Breadcrumbs::for('supper-admin.faqs.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Câu hỏi thường gặp');
});

Breadcrumbs::for('supper-admin.faqs.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.faqs.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.faqs.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.faqs.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.notes.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Ghi chú');
});

Breadcrumbs::for('supper-admin.notes.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.notes.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.notes.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.notes.index');
    $trail->push('Chỉnh sửa');
});


Breadcrumbs::for('supper-admin.posts.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Bài viết');
});

Breadcrumbs::for('supper-admin.posts.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.posts.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.notifications.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Ghi chú');
});

Breadcrumbs::for('supper-admin.notifications.create', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.notifications.index');
    $trail->push('Thêm mới');
});

Breadcrumbs::for('supper-admin.notifications.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.notifications.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.posts.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.posts.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.orders.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Đơn hàng');
});

Breadcrumbs::for('supper-admin.orders.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.orders.index');
    $trail->push('Chỉnh sửa');
});
Breadcrumbs::for('supper-admin.transactions.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Tổng giao dịch');
});


Breadcrumbs::for('supper-admin.tenants.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('admin.dashboard');
    $trail->push('Website đại lý');
});


Breadcrumbs::for('supper-admin.tenants.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.tenants.index');
    $trail->push('Chỉnh sửa');
});

Breadcrumbs::for('supper-admin.tenants.settings.edit', function (BreadcrumbsTrait $trail) {
    $trail->parent('supper-admin.tenants.index');
    $trail->push('Cài đặt');
});

Breadcrumbs::for('client.home', function (BreadcrumbsTrait $trail) {
    $trail->push('Trang chủ');
});
Breadcrumbs::for('client.deposit.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('client.home');
    $trail->push('Nạp tiền');
});
Breadcrumbs::for('client.transactions.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('client.home');
    $trail->push('Giao dịch');
});
Breadcrumbs::for('client.report.index', function (BreadcrumbsTrait $trail) {
    $trail->parent('client.home');
    $trail->push('Báo cáo');
});
Breadcrumbs::for('client.service.index', function ($trail, $platform, $category) {
    $trail->parent('client.home');

    $category = Category::query()->where('key', $category)->first();
    if ($category) {
        $trail->push($category->name, route('client.service.index', [$platform, $category]));
    } else {
        $trail->push('Danh mục không tồn tại');
    }
});
